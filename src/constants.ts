/* eslint-disable padding-line-between-statements */
import { i18n } from "@translations/i18n";
import { Search, ShoppingCartOutlined, ContentPasteSearchOutlined } from "@mui/icons-material";
import { EQuickActions } from "@features/crm/QuickActions/EQuickActions";
import { EPaymentType } from "@modules/payments/constants/EPaymentType";
import { ETransactionType } from "@features/crm/AccountTransaction/TransactionHistory/ITransactionsHistory";
import { EHistoryLevel } from "@features/crm/CustomerHistory/History/HistoryFilter/IHistoryFilter";
import { EYesNoNoneValues } from "@common/interfaces/EYesNoNoneValues";
import { IOption } from "@common";

export const DATE_TIME_FORMAT = "dd/MM/yyyy HH:mm:ss";

export const URL_DATE_FORMAT = "dd-MM-yyyy";

export const DATE_FORMAT = "dd/MM/yyyy";

export const OTP_GENERATION_LIMIT = 3;

export const TIME_FORMAT = "HH:mm";

export const CORREO_SMS = "Correo/Sms";

export const APPOINTMENT_DATE_FORMAT = "EEEE, dd.MM.yyyy";

export const API_DATE_FORMAT = "yyyy-MM-dd";

export const DATE_EXPIRY_FORMAT = "MM/yyyy";

export const DATE_FORMAT_MMMM_YYYY = "MMMM yyyy";

export const DATE_FORMAT_YYYY_MM = "yyyyMM";

export const DATE_FORMAT_LITERAL = "eeee MMMM dd";

export const DATE_TIMEZONE = "America/Guatemala";

export const NO_TECHNOLOGY_DATE = new Date(Date.UTC(2099, 11, 31));

export const CBS_TYPE_C_MAIN_BILLING_ACCOUNT = "C_MAIN_BILLING_ACCOUNT";

export const TOP_UP_AMOUNT_LIST = [1000, 1500, 2000, 2500, 3000, 3500, 4000];

export const CONTACT_ADDRES_TYPE_BILLING = "BILLING";

export const CONTACT_ADDRES_TYPE_INSTALLATION = "INSTALLATION";

export const REASON_CODE_INSTALL = "INSTALL";

export const SOURCE_CODE_UI = "UI";

export const TIME_SLOT_CODE_AM = "AM";

export const TIME_SLOT_CODE_PM = "PM";

export const SUSPENSION_TYPE = {
    FRAUD: "FRAUD",
    VOLUNTEER: "VOLUNTEER",
};

export enum ESERVICE_GROUP {
    MOBILE = "MOBILE",
    MBB = "MBB",
    DUALPLAY = "DUALPLAY",
    TV = "TV",
    BROADBAND_FMS = "BROADBAND_FMS",
    HFC = "HFC",
    INTERNET = "INTERNET",
    TELEPHONY = "TELEPHONY",
}

export enum FILE_UPLOAD_ALLOWED_TYPES {
    PDF = "application/pdf",
    DOC = "application/msword",
    DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    XLS = "application/vnd.ms-excel",
    XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    JPG = "image/jpeg",
    PNG = "image/png",
    BMP = "image/bmp",
    MSG = "application/msg",
}

export const MAX_FILE_SIZE_IN_MB = process.env.UPLOAD_MAX_FILE_SIZE_IN_MB;

export const MAX_FILE_SIZE_IN_BYTES = MAX_FILE_SIZE_IN_MB * 1024 * 1024;

export const SERVICE_GROUP_DISCOUNT = "SERVICE_GROUP_DISCOUNT";

export const CHANNEL_DIRECT = "DIRECT";

export const APPOINTMENT_DELTA_DAYS = 3;

export const CANCEL_VISIT_REASONS = {
    CANCELADO: "CANCELADO",
    FALLIDO_SIN_VISITA: "FALLIDO SIN VISITA",
};

export const CANCEL_VISIT_API_MESSAGES = {
    [CANCEL_VISIT_REASONS.CANCELADO]: "CUSTOMER NO LONGER REQUIRES THE SERVICE",
    [CANCEL_VISIT_REASONS.FALLIDO_SIN_VISITA]: "SERVICE RESTORED",
};

export const APPOINTMENT_NOTE_TITLE = "CRM-UI-Cancell";
export const APPOINTMENT_NOTE_TYPE = "Cancell";
export const APPOINTMENT_STATUS_CANCELED = "Cancelado";

export enum EAddonItemStatusResponse {
    NO_ONGOING_REQUEST = "NO_ONGOING_REQUEST",
    ONGOING_ADDITION = "ONGOING_ADDITION",
    ONGOING_TERMINATION = "ONGOING_TERMINATION",
}

export const ACCOUNT_TYPE = [
    { value: "", label: i18n.t("portin:none") },
    { value: "BILL_PAY_SINGLE_LINE", label: i18n.t("portin:postpaySingle") },
    { value: "BILL_PAY_MULTI_LINE", label: i18n.t("portin:postpayMulti") },
    { value: "PAYG_REGISTERED", label: i18n.t("portin:prepayRegistered") },
    { value: "PAYG_UNREGISTERED", label: i18n.t("portin:prepayUnregistered") },
];

export const ACCOUNT_TYPE_LIST_POSTPAY = [
    { value: "BILL_PAY_SINGLE_LINE", label: i18n.t("portin:postpaySingle") },
    { value: "BILL_PAY_MULTI_LINE", label: i18n.t("portin:postpayMulti") },
    { value: "PAYG_REGISTERED", label: i18n.t("portin:prepayRegistered") },
    { value: "PAYG_REGISTERED_MULTI_LINE", label: i18n.t("portin:prepayRegisteredMulti") },
    { value: "PAYG_UNREGISTERED", label: i18n.t("portin:prepayUnregistered") },
];

// Quick Actions
export const CUSTOMER_ACTIONS_OPTIONS = [
    {
        label: i18n.t("quickActions:tempUnlock"),
        value: EQuickActions.TEMP_UNLOCK,
    },
    {
        label: i18n.t("quickActions:verifyEmail"),
        value: EQuickActions.VERIFY_EMAIL,
    },
    {
        label: i18n.t("quickActions:passwordReset"),
        value: EQuickActions.UPDATE_PASSWORD,
    },
];

export const TRANSACTION_TYPE_LIST = [
    { label: i18n.t("transaction:transactionType.accountCreation"), value: ETransactionType.ACCOUNT_CREATION },
    { label: i18n.t("transaction:transactionType.refund"), value: ETransactionType.BALANCE_CLEARING_REFUND },
    { label: i18n.t("transaction:transactionType.adjustment"), value: ETransactionType.IMMEDIATE_ADJUSTMENT },
    { label: i18n.t("transaction:transactionType.invoice"), value: ETransactionType.INVOICE },
    { label: i18n.t("transaction:transactionType.payment"), value: ETransactionType.PAYMENT },
    { label: i18n.t("transaction:transactionType.paymentRejection"), value: ETransactionType.PAYMENT_BACKOUT },
    {
        label: i18n.t("transaction:transactionType.paymentTransfer"),
        value: ETransactionType.PAYMENT_REALLOCATION,
    },
    { label: i18n.t("transaction:transactionType.accountEnd"), value: ETransactionType.ACCOUNT_END },
    { label: i18n.t("transaction:transactionType.balanceTransfer"), value: ETransactionType.BALANCE_TRANSFER },
    { label: i18n.t("transaction:transactionType.prepaymentRelease"), value: ETransactionType.PREPAYMENT_RELEASE },
    { label: i18n.t("transaction:transactionType.creditLost"), value: ETransactionType.CREDIT_LOST },
];

export const CUSTOMER_FOUND_IN_CMS = "customer-found-in-cms";
export const PREQUAL_DATA_MISMATCH = "prequal-data-mismatch";
export const LINE_UNKNOWN = "line-unknown";
export const DIFFERENT_PHONE_NUMBER = "different-prequal-phone-number";
export const QUERY_ACCOUNT_NOT_FOUND = "query-account-not-found";
export const BROADBAND_SERVICE_NOT_ACCEPTED = "broadband-service-not-accepted";
export const SERVICE_CODE_NOT_ACCEPTED = "service-code-not-accepted";
export const AVAILABLE_SIM_CARD_NOT_FOUND = "available-sim-card-not-found";
export const NUMBER_NOT_AVAILABLE = "number-not-available";

// <editor-fold desc="ERROR CODE" defaultstate="collapsed">
// Port in
export const PORT_IN_STATUS_NOT_FOUND = "COULDNT_FIND_PORT";
// </editor-fold>

// <editor-fold desc="APPLICATION PAGES URL" defaultstate="collapsed">
const SEARCH_PAGE_URL = "search";
const HOME_PAGE_URL = `${SEARCH_PAGE_URL}/customer`;
const UG_PAGE_URL = "ug";
const CASE_MANAGEMENT_PAGE_URL = "caseManagement";
const CASE_MANAGEMENT_HOME_PAGE_URL = `${CASE_MANAGEMENT_PAGE_URL}/home/<USER>
export const CUSTOMER_PAGE_URL = "customer";
export const SUBSCRIPTION_PAGE_URL = "subscription";
export const ACQUISITION_PAGE_URL = "acquisition";
export const CHANGE_CHANNEL_URL = "channel";
export const ACQUISITION_JOURNEY = "journey";
export const ACQUISITION_PREPAY = "prepay";
export const ACQUISITION_POSTPAY = "postpay";
export const CROSS_SELL_POSTPAY = "postpay";
// export const ACQUISITION_JOURNEY_PAGE_URL = `${ACQUISITION_PAGE_URL}/${ACQUISITION_JOURNEY}`;
export const ACQUISITION_JOURNEY_PAGE_URL = "";

const ACQUISITION_CART_PAGE_URL = "cart";
const ACQUISITION_CUSTOMER_DETAIL_PAGE_URL = "customerDetail";
const ACQUISITION_SUMMARY_PAGE_URL = "summary";
const ACQUISITION_PAYMENT_PAGE_URL = "payment";
const ACQUISITION_OFFER_SELECTION_PAGE_URL = "offerSelection";
const ACQUISITION_OFFER_COMPOSITION_PAGE_URL = "offerComposition";
const ACQUISITION_CUSTOMER_INFO_PAGE_URL = "customerInfo";
const ACQUISITION_CREDIT_SCORE_PAGE_URL = "creditScore";
const ACQUISITION_ORDER_SUMMARY_PAGE_URL = "orderSummary";
const ACQUISITION_INSTALLATION_PAGE_URL = "installation";
const ACQUISITION_DEPOSIT_UPFRONT_PAGE_URL = "depositAndUpfront";
const ACQUISITION_RECURRING_PAYMENT_METHOD_PAGE_URL = "recurringPaymentMethod";
const ACQUISITION_UPFRONT_PAYMENT_METHOD_PAGE_URL = "upfrontPaymentMethod";
const ACQUISITION_ADDRESS_INSTALLATION_PAGE_URL = "installationAddress";
const ACQUISITION_CUSTOMER_ELIGIBILITY_PAGE_URL = "customerEligibility";
const ACQUISITION_ADDITIONAL_CONTACT_INFO_URL = "additionalClientInfo";
const ACQUISITION_INVOICE_DELIVERY_METHOD_URL = "invoiceDeliveryMethod";
const ACQUISITION_SCHEDULE_INSTALLATION_URL = "scheduleInstallation";
const CROSS_SELL_FIXED_ACCESSORIES_SELECTION_PAGE_URL = "accessoriesSelection";
const CROSS_SELL_FIXED_ACCESSORIES_CUSTOMER_INFO_PAGE_URL = "customerInfo";
const CROSS_SELL_FIXED_ACCESSORIES_ORDER_SUMMARY_PAGE_URL = "summary";
const CROSS_SELL_FIXED_ACCESSORIES_PAYMENT_PAGE_URL = "payment";
const ACQUISITION_CONTRACT_URL = "contract";

export const CRM_ACCOUNT_PAYMENT_PAGE_URL_LIST = {
    DIRECT_DEBIT: "direct_debit",
    CARD: "card",
};

export const CRM_CUSTOMER_HISTORY_DETAIL_URL_LIST = {
    SMS_URL: "sms",
    HISTORY_URL: "list",
    EMAIL_URL: "email",
};

export const ROLES_AND_PERMISSIONS = {
    VIEW_SUBSCRIPTIONS: "VIEW_SUBSCRIPTIONS",
    NEW_SUBSCRIPTION_PREPAID: "New_Subscription_PREPAID",
    NEW_SUBSCRIPTION_POSTPAID: "New_Subscription_POSTPAID",
    CASE_MANAGEMENT_BACKOFFICE: "CASE_MANAGEMENT_BACKOFFICE",
};

export const CLIENTS_OF_KEYCLOAK = {
    JOURNEY: "journey",
    CUSTOMER: "customer",
    CASE_MANAGEMENT: "galaxion-case-management",
};

export const STATUS_ADD_ONS = {
    ACTIVE: "ACTIVE",
    INACTIVE: "INACTIVE",
};

export const REASON_FOR_SUSPEND = [
    "Falta de uso",
    "Cambio de prioridades o necesidades",
    "Falta de Pago",
    "No se utilizará más el servicio ",
];

export const OFFER_TYPE_PARAM = "offerType";
export const HISTORY_ID_PARAM = "historyId";
export const ACCOUNT_ID_PARAM = "accountId";
export const SUBSCRIPTION_ID_PARAM = "subscriptionId";
export const PARENT_OFFER_CODE_PARAM = "parentOfferCode";
export const PARENT_SUBSCRIPTION_ID_PARAM = "parentSubscriptionId";
export const SEARCH_TYPE_PARAM = "searchType";
export const ORDER_REFERENCE_PARAM = "orderReference";
export const PROSPECT_REFERENCE_PARAM = "prospectReference";
export const CROSS_SELL_REFERENCE_PARAM = "crossSellReference";
export const OFFER_ID_PARAM = "offerId";
export const CHANGE_OFFER_UUID_PARAM = "changeOfferId";

export enum EOrderOrchestrator {
    OM = "orderManagement",
    WFE = "wfe",
    APPOINTMENTS = "appointments",
    XWFE = "xwfe",
}

export const CRM_ACCOUNT_DETAILS_URL_LIST = {
    PROFILE_PAGE_URL: "profile",
    PAYMENT_AND_BILLING_PAGE_URL: "payment_and_billing",
    CUSTOMER_HISTORY_PAGE_URL: "history",
    ORDER_PAGE_URL: "order",
    TRANSACTIONS_PAGE_URL: "transactions",
    COLLECTION_PAGE_URL: "collection",
    DOCUMENT_PAGE_URL: "documents",
    ACCESSORIES_PAGE_URL: "accessories",
    CREATE_SUBSCRIPTION_PAGE_URL: "create_subscription",
    ACCOUNT_ADDONS_PAGE_URL: "account_addons",
    CASE_MANAGEMENT_PAGE_URL: "casos",
    SUBSCRIPTIONS_ADD_ONS: "subscriptions-add-ons",
    INFORMATION_SCHEDULE: "informacion-agendas",
};

export const CRM_ACCOUNT_TRANSACTIONS_URL_LIST = {
    TRANSACTION_HISTORY_PAGE_URL: "transaction_history",
    PAYMENT_HISTORY_PAGE_URL: "payment_history",
};

export const CRM_SUBSCRIPTION_DETAILS_URL_LIST = {
    OFFER_PAGE_URL: "offer",
    BALANCE_PAGE_URL: "balance",
    USAGE_PAGE_URL: "usage",
    TOP_UP_PAGE_URL: "topUp",
    MODIFY_ADD_ONS: "addons",
    MANAGE_DISCOUNTS: "discounts",
    BARRING: "barring",
    SIM_REPLACEMENT: "sim",
    PORTING: "porting",
    TERMINATE_SUBSCRIPTION: "terminate_subscription",
    TERMINATE_BROADBAND: "terminate_broadband",
    TERMINATE_TV: "terminate_tv",
    TERMINATE_PSTN: "terminate_pstn",
    MSISDN_SWAP: "msisdn_swap",
    CHANGE_OFFER: "change_offer",
    CHANGE_OFFER_TV: "change_offer_tv",
    MANAGE_FEATURES: "manage_features",
    CHANGE_SPEED_PROFILE: "speed_profile",
    RESET_PORT: "reset_port",
    MANAGE_BROADBAND: "manage_broadband",
    MANAGE_HANDSET: "manage_handset",
    EQUIPMENT: "equipments",
    ACCESSORIES: "accessories",
    CHANGE_BROADBAND_EQUIPMENT: "change_broadband_equipment",
    MANAGE_VOBB_ADDONS: "vobb_addons",
    MANAGE_TV: "manage_tv",
    MANAGE_PSTN: "manage_pstn",
    MANAGE_VOBB: "manage_vobb",
    CHANGE_NUMBER: "change_number",
    MANAGE_TV_ADDONS: "tv_addons",
    MANAGE_TV_DEVICES_AND_PIN: "tv_devices_and_pin",
    MANAGE_BROADBAND_ADDONS: "broadband_addons",
    MANAGE_BROADBAND_BARRINGS: "broadband_barrings",
    MANAGE_PSTN_ADDONS: "pstn_addons",
    VOBB_SETTINGS: "vobb_settings",
    MANAGE_VOBB_BARRINGS: "vobb_barrings",
    VOBB_USAGE: "vobb_usage",
    MANAGE_CREDIT_LIMIT: "credit_limit",
    PSTN_USAGE: "pstn_usage",
    BAR_PTSN: "bar_pstn",
    MANAGE_PSTN_BARRINGS: "pstn_barrings",
};

export const CRM_CHANGE_OFFER_RENEWAL_URL_LIST = {
    RECONTRACT_PAGE_URL: "",
    SELECT_OFFER_PAGE_URL: "select",
    OFFER_DETAILS_PAGE_URL: "details",
    OFFER_COMPOSITION_PAGE_URL: "composition",
    SUMMARY_PAGE_URL: "summary",
    DEPOSIT_PAGE_URL: "deposit",
    PAYMENT_PAGE_URL: "payment",
    INSTALLATION_PAGE_URL: "installation",
};
export const ADDITIONAL_CONTACT_INFORMATION_URL = {
    ADD_CONTACT_INFO_URL: "addcontactinfo",
};
export const ACQUISITION_URLS = {
    ACQUISITION_CART_PAGE_URL,
    ACQUISITION_CUSTOMER_DETAIL_PAGE_URL,
    ACQUISITION_SUMMARY_PAGE_URL,
    ACQUISITION_PAYMENT_PAGE_URL,
    ACQUISITION_OFFER_SELECTION_PAGE_URL,
    ACQUISITION_OFFER_COMPOSITION_PAGE_URL,
    ACQUISITION_CUSTOMER_INFO_PAGE_URL,
    ACQUISITION_CREDIT_SCORE_PAGE_URL,
    ACQUISITION_ORDER_SUMMARY_PAGE_URL,
    ACQUISITION_INSTALLATION_PAGE_URL,
    ACQUISITION_DEPOSIT_UPFRONT_PAGE_URL,
    ACQUISITION_RECURRING_PAYMENT_METHOD_PAGE_URL,
    ACQUISITION_UPFRONT_PAYMENT_METHOD_PAGE_URL,
    ACQUISITION_ADDRESS_INSTALLATION_PAGE_URL,
    ACQUISITION_CUSTOMER_ELIGIBILITY_PAGE_URL,
    ACQUISITION_ADDITIONAL_CONTACT_INFO_URL,
    ACQUISITION_INVOICE_DELIVERY_METHOD_URL,
    ACQUISITION_SCHEDULE_INSTALLATION_URL,
    ACQUISITION_CONTRACT_URL,
};

export const CROSS_SELL_FIXED_ACCESSORIES_URLS = {
    CROSS_SELL_FIXED_ACCESSORIES_SELECTION_PAGE_URL,
    CROSS_SELL_FIXED_ACCESSORIES_CUSTOMER_INFO_PAGE_URL,
    CROSS_SELL_FIXED_ACCESSORIES_ORDER_SUMMARY_PAGE_URL,
    CROSS_SELL_FIXED_ACCESSORIES_PAYMENT_PAGE_URL,
};

export const URLS = {
    HOME_PAGE_URL,
    ACQUISITION_PAGE_URL,
    CUSTOMER_PAGE_URL,
    SEARCH_PAGE_URL,
    CHANGE_CHANNEL_URL,
    UG_PAGE_URL,
    CASE_MANAGEMENT_PAGE_URL,
    CASE_MANAGEMENT_HOME_PAGE_URL,
};

export const SIDE_MENU_ITEM_LIST = [
    {
        label: i18n.t("common:search"),
        icon: Search,
        url: `/${URLS.SEARCH_PAGE_URL}`,
        resourceAccess: [],
    },
    {
        label: i18n.t("common:acquisition"),
        icon: ShoppingCartOutlined,
        url: `/${URLS.ACQUISITION_PAGE_URL}`,
        resourceAccess: [],
    },
    {
        label: i18n.t("caseManagement:caseManagement"),
        icon: ContentPasteSearchOutlined,
        url: `/${URLS.CASE_MANAGEMENT_HOME_PAGE_URL}`,
        resourceAccess: [CLIENTS_OF_KEYCLOAK.CASE_MANAGEMENT],
    },
    // {
    //     label: i18n.t("common:ug"),
    //     icon: NotificationsOutlined,
    //     url: `/${URLS.UG_PAGE_URL}`,
    // },
];

export const MT_HEADER_V1 = {
    Accept: "application/vnd.monacotelecom.v1+json",
    "Content-Type": "application/vnd.monacotelecom.v1+json",
};

export const ADDONS_TV = "TV_CHANNEL";
export const ADDONS_EQUIPMENT = "ADDONS_EQUIPMENT";
export const ADDONS_INTERNET = "OTT";
export const ADDONS_MATERIAL = "MATERIAL";

export const MT_HEADER = {
    Accept: "application/vnd.monacotelecom.v2+json",
    "Content-Type": "application/vnd.monacotelecom.v2+json",
};

export const OLD_MERGE_PATCH_HEADER = {
    "Content-Type": "application/json",
};

export const BRAND = process.env.APP_BRAND;

export enum EACCOUNT_TYPE {
    B2C = "B2C",
}

export const PAYMENT_TYPE_LIST = {
    [EPaymentType.PAYMENT]: i18n.t("transaction:paymentType.payment"),
    [EPaymentType.REFUND]: i18n.t("transaction:paymentType.refund"),
    [EPaymentType.PAYMENT_TRANSFER]: i18n.t("transaction:paymentType.paymentTransfer"),
};

export const ROWS_PER_PAGE = 10;

export enum ESubscriptionStatus {
    INITIAL = "INITIAL",
    ACTIVE = "ACTIVE",
    TERMINATED = "TERMINATED",
}

export enum EContactPermissionGroup {
    ACTIVE_CUSTOMER = "ACTIVE_CUSTOMER",
    NO_LONGER_CUSTOMER = "NO_LONGER_CUSTOMER",
}

export enum EContactPermission {
    FOTS = "FOTS",
}

export const PDF = ".pdf";
export const PDF_XLS_DOCX = ".pdf, .doc, .docx, .xlsx, .xls";

export const FORM_DATA_ENC_TYPE = "multipart/form-data";

export const FORM_DATA_HEADER = { "Content-Type": FORM_DATA_ENC_TYPE };

export const ACCEPT_CSV_HEADER = { Accept: "text/csv" };

export const PROSPECT_COMPATIBILITY_ERROR = "PROSPECT_COMPATIBILITY";

export const STORAGE_KEY_SELECTED_CHANNEL = "USER_SELECTED_CHANNEL";

const USAGE_CHARGED_NON_ZERO = "NON_ZERO";
const USAGE_CHARGED_ZERO = "ZERO";

export const USAGE_CHARGE_LIST = [
    {
        value: i18n.t("common:all"),
        label: i18n.t("common:all"),
    },
    {
        value: USAGE_CHARGED_NON_ZERO,
        label: i18n.t("common:yes"),
    },
    {
        value: USAGE_CHARGED_ZERO,
        label: i18n.t("common:no"),
    },
];

export const YES_NO_OPTIONS = [
    {
        value: EYesNoNoneValues.YES,
        label: i18n.t("common:yes"),
    },
    {
        value: EYesNoNoneValues.NO,
        label: i18n.t("common:no"),
    },
];

export const CUSTOMER_HISTORY_LEVEL = [
    { label: i18n.t("customerHistory:levelType.all"), value: EHistoryLevel.ALL },
    { label: i18n.t("customerHistory:levelType.account"), value: EHistoryLevel.ACCOUNT },
    { label: i18n.t("customerHistory:levelType.subscription"), value: EHistoryLevel.SUBSCRIPTION },
];

export enum ECommitmentDuration {
    "ONE" = 1,
    "TWO" = 2,
    "THREE" = 3,
    "SIX" = 6,
    "TWELVE" = 12,
    "EIGHTEEN" = 18,
    "TWENTY_FOUR" = 24,
    "THIRTY_SIX" = 36,
}

/* ### Portin ### */

export enum EPortInState {
    INITIAL = "INITIAL",
    SCHEDULED = "SCHEDULED",
    IN_PROGRESS = "IN_PROGRESS",
}

export enum EPortInType {
    BILL_PAY_SINGLE_LINE = "BILL_PAY_SINGLE_LINE",
    BILL_PAY_MULTI_LINE = "BILL_PAY_MULTI_LINE",
    BILL_PAY_BULK = "BILL_PAY_BULK",
    PAYG_REGISTERED = "PAYG_REGISTERED",
    PAYG_UNREGISTERED = "PAYG_UNREGISTERED",
    PAYG_MULTI_LINE = "PAYG_MULTI_LINE",
}

export enum EPortInDirection {
    PORT_IN = "PORT_IN",
}

export enum EDeliveryType {
    ONLINE = "ONLINE",
    POSTAL = "POSTAL",
}

export const PAYMENT_REASON = [
    i18n.t("transaction:ddFailed"),
    i18n.t("transaction:inCollection"),
    i18n.t("transaction:cashPayer"),
    i18n.t("transaction:monthsInAdvance"),
    i18n.t("transaction:onAccount"),
];

export enum EServiceTechnology {
    FTTC = "FTTC", // broadband fiber to the cabinet
    FTTH = "FTTH", // fiber to the home
    NBI = "NBI", // fiber by the government
    VOBB = "VOBB", // Voice over broadband
    NGB = "NGB", // broadband adsl
    MBB = "MBB", // mobile broadband
    PSTN = "PSTN", // fixe
    SABB = "SABB", // stand-alone broadband
    LSH = "LSH",
    BS = "BS",
    VC = "VC",
    BEA = "BEA",
    VEA = "VEA",
    GEA = "GEA",
}

export const itemAdditionSeparator = " + ";

export const HANDSET_EQUIPMENT_TYPE = "HANDSET";
export const CSR_TAG = "CSR";

export const CONTRACT_ALREADY_SENT_ERROR = "contract-already-sent";

export enum EProvisionBroadband {
    PNN = "PNN",
    PNO = "PNO",
    PNW = "PNW",
}

export enum EProvisionVobb {
    GNP = "GNP",
    PVP = "PVP",
    XVP = "XVP",
}

export const PHONE_NUMBER_PREFIX = [
    "01",
    "021",
    "022",
    "023",
    "024",
    "025",
    "026",
    "027",
    "028",
    "029",
    "0402",
    "0404",
    "041",
    "042",
    "043",
    "044",
    "045",
    "046",
    "047",
    "049",
    "0504",
    "0505",
    "051",
    "052",
    "053",
    "056",
    "057",
    "058",
    "059",
    "061",
    "062",
    "063",
    "064",
    "065",
    "066",
    "067",
    "068",
    "069",
    "071",
    "074",
    "076",
    "090",
    "091",
    "093",
    "094",
    "095",
    "096",
    "097",
    "098",
    "099",
    "085",
    "086",
    "087",
    "083",
    "088",
    "089",
    "8881",
    "8882",
    "8883",
];

export enum EHttpCode {
    FORBIDDEN = 403,
}

export const NOT_IMPLEMENTED_ERROR = "NOT_IMPLEMENTED";

export const COUNTRY_LIST: IOption[] = [
    {
        label: "Malta",
        value: "MT",
    },
    {
        label: "Afghanistan",
        value: "AF",
    },
    {
        label: "Albania",
        value: "AL",
    },
    {
        label: "Algeria",
        value: "DZ",
    },
    {
        label: "American Samoa",
        value: "AS",
    },
    {
        label: "Andorra",
        value: "AD",
    },
    {
        label: "Angola",
        value: "AO",
    },
    {
        label: "Anguilla",
        value: "AI",
    },
    {
        label: "Antarctica",
        value: "AQ",
    },
    {
        label: "Antigua and Barbuda",
        value: "AG",
    },
    {
        label: "Argentina",
        value: "AR",
    },
    {
        label: "Armenia",
        value: "AM",
    },
    {
        label: "Aruba",
        value: "AW",
    },
    {
        label: "Australia",
        value: "AU",
    },
    {
        label: "Austria",
        value: "AT",
    },
    {
        label: "Azerbaijan",
        value: "AZ",
    },
    {
        label: "Bahamas",
        value: "BS",
    },
    {
        label: "Bahrain",
        value: "BH",
    },
    {
        label: "Bangladesh",
        value: "BD",
    },
    {
        label: "Barbados",
        value: "BB",
    },
    {
        label: "Belarus",
        value: "BY",
    },
    {
        label: "Belgium",
        value: "BE",
    },
    {
        label: "Belize",
        value: "BZ",
    },
    {
        label: "Benin",
        value: "BJ",
    },
    {
        label: "Bermuda",
        value: "BM",
    },
    {
        label: "Bhutan",
        value: "BT",
    },
    {
        label: "Bolivia (Plurinational State of)",
        value: "BO",
    },
    {
        label: "Bonaire, Sint Eustatius and Saba",
        value: "BQ",
    },
    {
        label: "Bosnia and Herzegovina",
        value: "BA",
    },
    {
        label: "Botswana",
        value: "BW",
    },
    {
        label: "Bouvet Island",
        value: "BV",
    },
    {
        label: "Brazil",
        value: "BR",
    },
    {
        label: "British Indian Ocean Territory",
        value: "IO",
    },
    {
        label: "Brunei Darussalam",
        value: "BN",
    },
    {
        label: "Bulgaria",
        value: "BG",
    },
    {
        label: "Burkina Faso",
        value: "BF",
    },
    {
        label: "Burundi",
        value: "BI",
    },
    {
        label: "Cabo Verde",
        value: "CV",
    },
    {
        label: "Cambodia",
        value: "KH",
    },
    {
        label: "Cameroon",
        value: "CM",
    },
    {
        label: "Canada",
        value: "CA",
    },
    {
        label: "Cayman Islands",
        value: "KY",
    },
    {
        label: "Central African Republic",
        value: "CF",
    },
    {
        label: "Chad",
        value: "TD",
    },
    {
        label: "Chile",
        value: "CL",
    },
    {
        label: "China",
        value: "CN",
    },
    {
        label: "Christmas Island",
        value: "CX",
    },
    {
        label: "Cocos (Keeling) Islands",
        value: "CC",
    },
    {
        label: "Colombia",
        value: "CO",
    },
    {
        label: "Comoros",
        value: "KM",
    },
    {
        label: "Congo",
        value: "CG",
    },
    {
        label: "Congo, Democratic Republic of the",
        value: "CD",
    },
    {
        label: "Cook Islands",
        value: "CK",
    },
    {
        label: "Costa Rica",
        value: "CR",
    },
    {
        label: "Côte d'Ivoire",
        value: "CI",
    },
    {
        label: "Croatia",
        value: "HR",
    },
    {
        label: "Cuba",
        value: "CU",
    },
    {
        label: "Curaçao",
        value: "CW",
    },
    {
        label: "Cyprus",
        value: "CY",
    },
    {
        label: "Czechia",
        value: "CZ",
    },
    {
        label: "Denmark",
        value: "DK",
    },
    {
        label: "Djibouti",
        value: "DJ",
    },
    {
        label: "Dominica",
        value: "DM",
    },
    {
        label: "Dominican Republic",
        value: "DO",
    },
    {
        label: "Ecuador",
        value: "EC",
    },
    {
        label: "Egypt",
        value: "EG",
    },
    {
        label: "El Salvador",
        value: "SV",
    },
    {
        label: "Equatorial Guinea",
        value: "GQ",
    },
    {
        label: "Eritrea",
        value: "ER",
    },
    {
        label: "Estonia",
        value: "EE",
    },
    {
        label: "Eswatini",
        value: "SZ",
    },
    {
        label: "Ethiopia",
        value: "ET",
    },
    {
        label: "Falkland Islands (Malvinas)",
        value: "FK",
    },
    {
        label: "Faroe Islands",
        value: "FO",
    },
    {
        label: "Fiji",
        value: "FJ",
    },
    {
        label: "Finland",
        value: "FI",
    },
    {
        label: "France",
        value: "FR",
    },
    {
        label: "French Guiana",
        value: "GF",
    },
    {
        label: "French Polynesia",
        value: "PF",
    },
    {
        label: "French Southern Territories",
        value: "TF",
    },
    {
        label: "Gabon",
        value: "GA",
    },
    {
        label: "Gambia",
        value: "GM",
    },
    {
        label: "Georgia",
        value: "GE",
    },
    {
        label: "Germany",
        value: "DE",
    },
    {
        label: "Ghana",
        value: "GH",
    },
    {
        label: "Gibraltar",
        value: "GI",
    },
    {
        label: "Greece",
        value: "GR",
    },
    {
        label: "Greenland",
        value: "GL",
    },
    {
        label: "Grenada",
        value: "GD",
    },
    {
        label: "Guadeloupe",
        value: "GP",
    },
    {
        label: "Guam",
        value: "GU",
    },
    {
        label: "Guatemala",
        value: "GT",
    },
    {
        label: "Guernsey",
        value: "GG",
    },
    {
        label: "Guinea",
        value: "GN",
    },
    {
        label: "Guinea-Bissau",
        value: "GW",
    },
    {
        label: "Guyana",
        value: "GY",
    },
    {
        label: "Haiti",
        value: "HT",
    },
    {
        label: "Heard Island and McDonald Islands",
        value: "HM",
    },
    {
        label: "Holy See",
        value: "VA",
    },
    {
        label: "Honduras",
        value: "HN",
    },
    {
        label: "Hong Kong",
        value: "HK",
    },
    {
        label: "Hungary",
        value: "HU",
    },
    {
        label: "Iceland",
        value: "IS",
    },
    {
        label: "India",
        value: "IN",
    },
    {
        label: "Indonesia",
        value: "ID",
    },
    {
        label: "Iran (Islamic Republic of)",
        value: "IR",
    },
    {
        label: "Iraq",
        value: "IQ",
    },
    {
        label: "Ireland",
        value: "IE",
    },
    {
        label: "Isle of Man",
        value: "IM",
    },
    {
        label: "Israel",
        value: "IL",
    },
    {
        label: "Italy",
        value: "IT",
    },
    {
        label: "Jamaica",
        value: "JM",
    },
    {
        label: "Japan",
        value: "JP",
    },
    {
        label: "Jersey",
        value: "JE",
    },
    {
        label: "Jordan",
        value: "JO",
    },
    {
        label: "Kazakhstan",
        value: "KZ",
    },
    {
        label: "Kenya",
        value: "KE",
    },
    {
        label: "Kiribati",
        value: "KI",
    },
    {
        label: "Korea (Democratic People's Republic of)",
        value: "KP",
    },
    {
        label: "Korea, Republic of",
        value: "KR",
    },
    {
        label: "Kuwait",
        value: "KW",
    },
    {
        label: "Kyrgyzstan",
        value: "KG",
    },
    {
        label: "Lao People's Democratic Republic",
        value: "LA",
    },
    {
        label: "Latvia",
        value: "LV",
    },
    {
        label: "Lebanon",
        value: "LB",
    },
    {
        label: "Lesotho",
        value: "LS",
    },
    {
        label: "Liberia",
        value: "LR",
    },
    {
        label: "Libya",
        value: "LY",
    },
    {
        label: "Liechtenstein",
        value: "LI",
    },
    {
        label: "Lithuania",
        value: "LT",
    },
    {
        label: "Luxembourg",
        value: "LU",
    },
    {
        label: "Macao",
        value: "MO",
    },
    {
        label: "Madagascar",
        value: "MG",
    },
    {
        label: "Malawi",
        value: "MW",
    },
    {
        label: "Malaysia",
        value: "MY",
    },
    {
        label: "Maldives",
        value: "MV",
    },
    {
        label: "Mali",
        value: "ML",
    },
    {
        label: "Marshall Islands",
        value: "MH",
    },
    {
        label: "Martinique",
        value: "MQ",
    },
    {
        label: "Mauritania",
        value: "MR",
    },
    {
        label: "Mauritius",
        value: "MU",
    },
    {
        label: "Mayotte",
        value: "YT",
    },
    {
        label: "Mexico",
        value: "MX",
    },
    {
        label: "Micronesia (Federated States of)",
        value: "FM",
    },
    {
        label: "Moldova, Republic of",
        value: "MD",
    },
    {
        label: "Monaco",
        value: "MC",
    },
    {
        label: "Mongolia",
        value: "MN",
    },
    {
        label: "Montenegro",
        value: "ME",
    },
    {
        label: "Montserrat",
        value: "MS",
    },
    {
        label: "Morocco",
        value: "MA",
    },
    {
        label: "Mozambique",
        value: "MZ",
    },
    {
        label: "Myanmar",
        value: "MM",
    },
    {
        label: "Namibia",
        value: "NA",
    },
    {
        label: "Nauru",
        value: "NR",
    },
    {
        label: "Nepal",
        value: "NP",
    },
    {
        label: "Netherlands",
        value: "NL",
    },
    {
        label: "New Caledonia",
        value: "NC",
    },
    {
        label: "New Zealand",
        value: "NZ",
    },
    {
        label: "Nicaragua",
        value: "NI",
    },
    {
        label: "Niger",
        value: "NE",
    },
    {
        label: "Nigeria",
        value: "NG",
    },
    {
        label: "Niue",
        value: "NU",
    },
    {
        label: "Norfolk Island",
        value: "NF",
    },
    {
        label: "Northern Mariana Islands",
        value: "MP",
    },
    {
        label: "Norway",
        value: "NO",
    },
    {
        label: "Oman",
        value: "OM",
    },
    {
        label: "Pakistan",
        value: "PK",
    },
    {
        label: "Palau",
        value: "PW",
    },
    {
        label: "Palestine, State of",
        value: "PS",
    },
    {
        label: "Panama",
        value: "PA",
    },
    {
        label: "Papua New Guinea",
        value: "PG",
    },
    {
        label: "Paraguay",
        value: "PY",
    },
    {
        label: "Peru",
        value: "PE",
    },
    {
        label: "Philippines",
        value: "PH",
    },
    {
        label: "Pitcairn",
        value: "PN",
    },
    {
        label: "Poland",
        value: "PL",
    },
    {
        label: "Portugal",
        value: "PT",
    },
    {
        label: "Puerto Rico",
        value: "PR",
    },
    {
        label: "Qatar",
        value: "QA",
    },
    {
        label: "Réunion",
        value: "RE",
    },
    {
        label: "Romania",
        value: "RO",
    },
    {
        label: "Russian Federation",
        value: "RU",
    },
    {
        label: "Rwanda",
        value: "RW",
    },
    {
        label: "Saint Barthélemy",
        value: "BL",
    },
    {
        label: "Saint Helena, Ascension and Tristan da Cunha",
        value: "SH",
    },
    {
        label: "Saint Kitts and Nevis",
        value: "KN",
    },
    {
        label: "Saint Lucia",
        value: "LC",
    },
    {
        label: "Saint Martin (French part)",
        value: "MF",
    },
    {
        label: "Saint Pierre and Miquelon",
        value: "PM",
    },
    {
        label: "Saint Vincent and the Grenadines",
        value: "VC",
    },
    {
        label: "Samoa",
        value: "WS",
    },
    {
        label: "San Marino",
        value: "SM",
    },
    {
        label: "Sao Tome and Principe",
        value: "ST",
    },
    {
        label: "Saudi Arabia",
        value: "SA",
    },
    {
        label: "Senegal",
        value: "SN",
    },
    {
        label: "Serbia",
        value: "RS",
    },
    {
        label: "Seychelles",
        value: "SC",
    },
    {
        label: "Sierra Leone",
        value: "SL",
    },
    {
        label: "Singapore",
        value: "SG",
    },
    {
        label: "Sint Maarten (Dutch part)",
        value: "SX",
    },
    {
        label: "Slovakia",
        value: "SK",
    },
    {
        label: "Slovenia",
        value: "SI",
    },
    {
        label: "Solomon Islands",
        value: "SB",
    },
    {
        label: "Somalia",
        value: "SO",
    },
    {
        label: "South Africa",
        value: "ZA",
    },
    {
        label: "South Georgia and the South Sandwich Islands",
        value: "GS",
    },
    {
        label: "South Sudan",
        value: "SS",
    },
    {
        label: "Spain",
        value: "ES",
    },
    {
        label: "Sri Lanka",
        value: "LK",
    },
    {
        label: "Sudan",
        value: "SD",
    },
    {
        label: "Suriname",
        value: "SR",
    },
    {
        label: "Svalbard and Jan Mayen",
        value: "SJ",
    },
    {
        label: "Sweden",
        value: "SE",
    },
    {
        label: "Switzerland",
        value: "CH",
    },
    {
        label: "Syrian Arab Republic",
        value: "SY",
    },
    {
        label: "Taiwan, Province of China",
        value: "TW",
    },
    {
        label: "Tajikistan",
        value: "TJ",
    },
    {
        label: "Tanzania, United Republic of",
        value: "TZ",
    },
    {
        label: "Thailand",
        value: "TH",
    },
    {
        label: "Timor-Leste",
        value: "TL",
    },
    {
        label: "Togo",
        value: "TG",
    },
    {
        label: "Tokelau",
        value: "TK",
    },
    {
        label: "Tonga",
        value: "TO",
    },
    {
        label: "Trinidad and Tobago",
        value: "TT",
    },
    {
        label: "Tunisia",
        value: "TN",
    },
    {
        label: "Turkey",
        value: "TR",
    },
    {
        label: "Turkmenistan",
        value: "TM",
    },
    {
        label: "Turks and Caicos Islands",
        value: "TC",
    },
    {
        label: "Tuvalu",
        value: "TV",
    },
    {
        label: "Uganda",
        value: "UG",
    },
    {
        label: "Ukraine",
        value: "UA",
    },
    {
        label: "United Arab Emirates",
        value: "AE",
    },
    {
        label: "United Kingdom of Great Britain and Northern Ireland",
        value: "GB",
    },
    {
        label: "United States of America",
        value: "US",
    },
    {
        label: "United States Minor Outlying Islands",
        value: "UM",
    },
    {
        label: "Uruguay",
        value: "UY",
    },
    {
        label: "Uzbekistan",
        value: "UZ",
    },
    {
        label: "Vanuatu",
        value: "VU",
    },
    {
        label: "Venezuela (Bolivarian Republic of)",
        value: "VE",
    },
    {
        label: "Viet Nam",
        value: "VN",
    },
    {
        label: "Virgin Islands (British)",
        value: "VG",
    },
    {
        label: "Virgin Islands (U.S.)",
        value: "VI",
    },
    {
        label: "Wallis and Futuna",
        value: "WF",
    },
    {
        label: "Western Sahara",
        value: "EH",
    },
    {
        label: "Yemen",
        value: "YE",
    },
    {
        label: "Zambia",
        value: "ZM",
    },
    {
        label: "Zimbabwe",
        value: "ZW",
    },
];

export const inventoryPoolCode = {
    MOBILE: "MOBILE",
    BROADBAND_FMS: "IOT",
    MBB: "",
    DUALPLAY: "",
    TV: "",
    HFC: "",
};

export const PHONE_NUMBER_LENGHT = 8;

export const MIN_LONG_FIELD_NAMES = 2;

export const SPECIAL_CHARACTERS = '°!"#$%&/()=?¡¡+*¨´{}[\\]';

export const REGEX_FIELD_NAMES = "";

export const BEGIN_ROW_NUM_CBS = 0;

export const FETCH_ROW_NUM_CBS = 3;

export const TOTAL_ROW_NUM_CBS = 3;

export enum APP_LANGUAGE {
    ES = "es",
    ES_GT = "es-GT",
    ES_PA = "es-PA",
}

export const ADDONS_PERMISSION = {
    PURCHASE_ADDONS: "PURCHASE_ADDON",
    CANCEL_ADDONS: "CANCEL_ADDON",
};

export const RULES_OF_USER = {
    PERMISSION_TYPE: {
        CUSTOMER: "customer",
        JOURNEY: "journey",
    },
    CUSTOMER: {
        VIEW_SUBSCRIPTIONS: "VIEW_SUBSCRIPTIONS",
        SUSPEND_VIEW_VOLUNTARY: "SUSPEND_VIEW_VOLUNTARY",
        SUSPEND_VIEW_FRAUD: "SUSPEND_VIEW_FRAUD",
        CANCEL_SUBSCRIPTION_VIEW_INMEDIATE: "CANCEL_SUBSCRIPTION_VIEW_INMEDIATE",
        CANCEL_SUBSCRIPTION_VIEW_FRAUD: "CANCEL_SUBSCRIPTION_VIEW_FRAUD",
        TRACK_VISIT: "TRACK_VISIT",
        SCHEDULE_VISIT: "SCHEDULE_VISIT",
        RESCHEDULE_VISIT: "RESCHEDULE_VISIT",
    },
    JOURNEY: {
        NEW_SUBSCRIPTION_POSTPAID: "New_Subscription_POSTPAID",
        NEW_SUBSCRIPTION_PREPAID: "New_Subscription_PREPAID",
    },
};

export const ADDONS_STATUS = {
    ACTIVE: "ACTIVE",
    ON_GOING_REQUEST: "ON_GOING_REQUEST",
    NO_ONGOING_REQUEST: "NO_ONGOING_REQUEST",
    INITIAL: "INITIAL",
    ONGOING_TERMINATION: "ONGOING_TERMINATION",
};
