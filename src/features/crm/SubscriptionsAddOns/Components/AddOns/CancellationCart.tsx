import {
    Box,
    <PERSON>rid,
    <PERSON>pography,
    Button,
    IconButton,
    Dialog,
    DialogActions,
    DialogContent,
    FormControl,
    FormGroup,
    FormControlLabel,
    Checkbox
} from "@mui/material";
import { ICollectionAddOns } from "@services/subscription/accountId/interface/IAddOns";
import DeleteIcon from "@mui/icons-material/Delete";
import { useState } from "react";
import { DialogTitleClose } from "@common/Dialog";
import { FormButton } from "@common/FormButton/FormButton";
import { useTranslation } from "react-i18next";
import { CancelAddOnDialog } from "@common/Dialog/CancelAddOnDialog/CancelAddOnDialog";
import ReScheduleConfirmationModal from "@common/styleComponents/OrderConfirmationModal/ReScheduleConfirmationModal";

interface IProps {
    addedAddons: ICollectionAddOns[];
    onConfirmCancellation?: (deliverInStore?: boolean) => Promise<void> | void;
    onRemoveAddon?: (addonId: number) => void;
    // Props necesarias para conectar los diálogos
    handleShowSchedule?: (show: boolean) => void;
    setRefreshAddons?: (value: boolean) => void;
    setShowCancellationAddon?: React.Dispatch<React.SetStateAction<boolean>>;
    OrderIdForSchedule: string;
    isCancellingAddons?: boolean;
}

const CancellationCart = ({
    addedAddons,
    onConfirmCancellation,
    onRemoveAddon,
    handleShowSchedule,
    setRefreshAddons,
    setShowCancellationAddon,
    OrderIdForSchedule,
    isCancellingAddons = false
}: IProps) => {
    const { t } = useTranslation(["common", "customer"]);
    const [IsModalDeliverInStore, setIsModalDeliverInStore] = useState(false);
    const [deliverInStore, setDeliverInStore] = useState(false);
    const [isConfirming, setIsConfirming] = useState(false);
    const [cancelMessage] = useState("");
    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [isReScheduleModalOpen, setIsReScheduleModalOpen] = useState(false);
    const [isMaterial, setIsMaterial] = useState(false);

 
 
    // MODAL SOBRE FORMA DE ENTREGA DE EQUIPO
    // Confirmar tipo de entrega de equipo (en tienda o por visita tecnica)
    const handleModalConfirmDelivery = async () => {
        setIsConfirming(true);
        //Haciendo llamada a WFE FACADE y a Addons-service
        await onConfirmCancellation?.(deliverInStore);
        setIsConfirming(false);
           
        //Se muestra el dialogo de confirmacion de la orden
        setIsReScheduleModalOpen(true);
        setIsModalDeliverInStore(false);
    };

    // Función para cerrar el modal
    const handleModalCloseDelivery = () => {
        setIsModalDeliverInStore(false);
        setDeliverInStore(false);
    };


    // MODAL QUE PREGUNTA SI ESTA SEGURO DE CANCELAR LOS ADDONS SELECCIONADOS
    // Función para manejar la cancelación de addons
    const handleQuestionCancellationAddOns = async (): Promise<string | number> => {
        try {
            // Verificar si hay addons con itemGroupCode "MATERIAL"
            const hasMaterial = addedAddons.some(addon => addon.itemGroupCode === "MATERIAL");
            setIsMaterial(hasMaterial);

            setShowCancelDialog(false);

            if (hasMaterial) {
                // Escenario A: Mostrar modal con checkbox
                setIsModalDeliverInStore(true);

            } else {
                // Escenario B: Se llama a WFE directamente
                // Para addons de tipo servicio, cancelación directa
                setIsConfirming(true);
                //Haciendo llamada a WFE FACADE y a Addons-service
                await onConfirmCancellation?.(deliverInStore);
                setIsConfirming(false);

                setIsReScheduleModalOpen(true);
            }

            return "";
        } catch (error: any) {
            console.error("Error en handleCancelCloseAddOns:", error);
            setIsConfirming(false);
            return error.message;
        }
    };

    // Función para mostrar el diálogo que solicita que se confirme la cancelacion
    const handleShowCancelDialog = () => {
        setShowCancelDialog(true);
    };




    const handleCloseModalConfirmationFinal = async () => {
        if (isMaterial) {
            //En este punto se decide como termina el flujo con agendamiento o entrega en tienda
            if(deliverInStore){
                //Mostra componente de entrega de equipos
                //console.log("Entrego en tienda");
            }else{
                //Mostrar componente de agendamiento
                handleShowSchedule?.(isMaterial);
                setShowCancellationAddon?.(false);
            }
        }else{
            setShowCancelDialog(false);
            setRefreshAddons?.(true);
            setShowCancellationAddon?.(false);
        }
        setDeliverInStore(false);
        setIsReScheduleModalOpen(false);
    };


    return (
        <Box sx={{ pb: 1, pt: 3, pl: 3, pr: 3 }}>
            <Typography color={"primary"} pb={3} textAlign={"center"} variant="h5">
                Addons a Cancelar
            </Typography>
            {addedAddons.length === 0 ? (
                <Box sx={{ textAlign: "center", py: 4 }}>
                    <Typography color="text.secondary" variant="body1">
                        No hay addons seleccionados para cancelar
                    </Typography>
                </Box>
            ) : (
                <>
                    {addedAddons.map((addon) => (

                        <Box
                            key={addon.id}
                            sx={{
                                mb: 1, 
                                p: 2,
                                border: "1px solid #e0e0e0",
                                //borderRadius: 1,
                                backgroundColor: "#fff"
                            }}
                        >
                            <Grid container spacing={2}>
                                <Grid item xs={10} container alignItems="center">
                                    <Grid container spacing={2}  alignItems="center">
                                        <Grid item xs={4}>
                                            <Typography color={"primary"} variant="subtitle1" fontWeight="bold">
                                                {addon.description}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={2}>
                                            <Typography variant="body2" color="text.secondary">
                                                ID: {addon.id}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={6}>
                                            <Typography variant="body2" color="text.secondary">
                                                Serial: {(() => {
                                                            const isMaterial = String(addon.itemGroupCode || "").toLowerCase() === "material";
                                            
                                                            if (!isMaterial) return "N/A"; 
                                            
                                                            const sn = addon.includedEquipmentsCodes?.[0]?.serialNumber;
                                                            const snText = sn == null ? "" : String(sn).trim();
                                            
                                                            if (!snText) return "No disponible";
                                                                                                        
                                                            return snText;
                                                        })()}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </Grid>
                                <Grid item xs={2} sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                                    <IconButton
                                        color="primary"
                                        onClick={() => onRemoveAddon?.(addon.id)}
                                        size="small"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </Grid>
                            </Grid>
                        </Box>
                    ))}
 
                </>
            )}
            <Box sx={{ marginTop: 3, textAlign: "center" }}>                        
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <Button
                            color="inherit"
                            fullWidth
                            variant="contained"
                            onClick={() => {
                              setShowCancellationAddon?.(false);
                            }}
                        >
                            {t("customer:cancelSubscriptionAddOns")}
                        </Button>
                    </Grid>
                    <Grid item xs={6}>
                        <Button
                            fullWidth
                            sx={{ mb: 2 }}
                            variant="contained"
                            onClick={(handleShowCancelDialog)}
                            disabled={addedAddons.length === 0 || isConfirming || isCancellingAddons}
                        >
                            {isCancellingAddons ? "Procesando..." : t("customer:confirm")}
                        </Button>
                    </Grid>
                </Grid>
            </Box>


            {/* Modal para equipos con itemGroupCode MATERIAL */}
            <Dialog
                open={IsModalDeliverInStore}
                onClose={handleModalCloseDelivery}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitleClose
                    handleClose={handleModalCloseDelivery}
                    title="Confirmación de Cancelación"
                />
                <DialogContent>
                    <Typography variant="body1" sx={{ mb: 3 }}>
                        Se detectaron equipos en la cancelación que requieren confirmación adicional.
                    </Typography>
                    <FormControl>
                        <FormGroup>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={deliverInStore}
                                        onChange={(e) => setDeliverInStore(e.target.checked)}
                                        color="primary"
                                    />
                                }
                                label="Los equipos a cancelar se entregan en tienda"
                            />
                        </FormGroup>
                    </FormControl>
                </DialogContent>
                <DialogActions sx={{ p: 2, gap: 1 }}>
                    <FormButton
                        buttonText={isCancellingAddons ? "Procesando..." : "Confirmar Cancelación"}
                        color="primary"
                        variant="contained"
                        onClick={handleModalConfirmDelivery}
                        disabled={isConfirming || isCancellingAddons}
                    />
                    <FormButton
                        buttonText={t("common:cancel")}
                        color="primary"
                        variant="outlined"
                        onClick={handleModalCloseDelivery}
                    />
                </DialogActions>
            </Dialog>
            {/*DIALOGO PARA CONFIRMAR CANCELACION*/}
            <CancelAddOnDialog
                handleCancel={() => setShowCancelDialog(false)}
                handleCancelCloseAddOns={() => {
                    handleQuestionCancellationAddOns();
                }}
                isCancellable={true}
                message={cancelMessage}
                open={showCancelDialog}
                orderCancelled={undefined}
                title={t("customer:titleCancelAddon")}
            />
            {/*DIALOGO CONFIRMACION CANCELACION MUESTRA EL ORDER ID*/}
            <ReScheduleConfirmationModal
                isOpen={isReScheduleModalOpen}
                message={t("common:cancelAddondsRequestLabel")}
                onClose={handleCloseModalConfirmationFinal}
                orderID={OrderIdForSchedule ? String(OrderIdForSchedule) : undefined}
            />
        </Box>
    );
};

export default CancellationCart;
